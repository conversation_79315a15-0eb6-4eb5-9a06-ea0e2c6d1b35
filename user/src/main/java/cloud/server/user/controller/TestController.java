package cloud.server.user.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import cloud.server.user.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "测试接口", description = "用于测试用户信息传递的接口")
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Operation(summary = "测试用户信息传递", description = "测试从网关传递过来的用户信息")
    @GetMapping("/user-info")
    public ApiResponse<Map<String, Object>> testUserInfo(
            @RequestHeader(value = "X-User-Info", required = false) String userInfoHeader,
            @RequestHeader(value = "X-User-Id", required = false) String userIdHeader) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (userInfoHeader != null) {
            try {
                // Base64解码
                String decodedUserInfo = Base64.decodeStr(userInfoHeader);
                log.info("Received user info from gateway: {}", decodedUserInfo);
                
                // 解析JSON
                Object userInfo = JSONUtil.parse(decodedUserInfo);
                result.put("userInfo", userInfo);
                result.put("source", "X-User-Info header (base64 encoded)");
            } catch (Exception e) {
                log.error("Error decoding user info", e);
                result.put("error", "Failed to decode user info: " + e.getMessage());
            }
        } else if (userIdHeader != null) {
            result.put("userId", userIdHeader);
            result.put("source", "X-User-Id header (fallback)");
        } else {
            result.put("message", "No user info found in headers");
        }
        
        return ApiResponse.success(result);
    }
}
