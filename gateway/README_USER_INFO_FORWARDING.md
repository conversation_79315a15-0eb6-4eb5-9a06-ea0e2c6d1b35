# 用户信息转发功能说明

## 功能概述

在网关层实现了用户信息的自动获取和转发功能。当用户通过 Sa-Token 认证后，网关会：

1. 通过 token 获取用户ID
2. 调用用户服务获取完整的用户信息
3. 将用户信息进行 Base64 编码
4. 添加到请求头中转发给下游微服务

## 实现原理

### 核心组件

1. **ConfigurableSaTokenFilter**: 可配置的认证过滤器
   - 执行 Sa-Token 登录检查
   - 获取用户ID并调用用户服务
   - 将用户信息编码后添加到请求头

2. **UserFeignClient**: Feign 客户端
   - 调用用户服务的 getUserById 接口
   - 自动添加 Same-Token 头部用于服务间认证

3. **FeignInterceptor**: Feign 拦截器
   - 自动为 Feign 请求添加 Same-Token

### 请求头说明

- **X-User-Info**: 包含完整用户信息的 Base64 编码字符串
- **X-User-Id**: 备用用户ID（当获取用户信息失败时使用）

## 配置说明

### 网关配置 (application.yml)

```yaml
# 认证配置
auth:
  excludeUris:
    - /api/auth/**
  enabled: true
  logEnabled: true
  userInfoHeaderName: X-User-Info

# 用户服务配置
user:
  service:
    baseUrl: http://127.0.0.1:8002
```

### 配置参数说明

- `auth.enabled`: 是否启用认证 (默认: true)
- `auth.excludeUris`: 排除认证的URI列表
- `auth.includeUris`: 包含认证的URI列表 (默认: /**)
- `auth.logEnabled`: 是否启用日志 (默认: true)
- `auth.userInfoHeaderName`: 用户信息请求头名称 (默认: X-User-Info)
- `user.service.baseUrl`: 用户服务基础URL

## 使用方法

### 在微服务中获取用户信息

```java
@RestController
public class ExampleController {
    
    @GetMapping("/example")
    public ApiResponse<String> example(
            @RequestHeader(value = "X-User-Info", required = false) String userInfoHeader) {
        
        if (userInfoHeader != null) {
            try {
                // Base64解码
                String decodedUserInfo = Base64.decodeStr(userInfoHeader);
                
                // 解析为用户对象
                UserVo userInfo = JSONUtil.toBean(decodedUserInfo, UserVo.class);
                
                // 使用用户信息
                log.info("Current user: {}", userInfo.getUserName());
                
            } catch (Exception e) {
                log.error("Error decoding user info", e);
            }
        }
        
        return ApiResponse.success("Success");
    }
}
```

### 用户信息结构

```json
{
  "userId": 1,
  "userName": "admin",
  "nickName": "管理员",
  "gender": 1,
  "email": "<EMAIL>",
  "phoneNumber": "13800138000",
  "avatar": "http://example.com/avatar.jpg",
  "avatarThumb": "http://example.com/avatar_thumb.jpg"
}
```

## 测试接口

提供了测试接口来验证用户信息传递：

```
GET /api/user/test/user-info
```

此接口会返回从网关传递过来的用户信息，用于验证功能是否正常工作。

## 错误处理

1. **用户服务调用失败**: 会返回包含用户ID的基本信息
2. **用户信息编码失败**: 会降级为只传递用户ID
3. **认证失败**: 返回401未授权错误

## 注意事项

1. 确保用户服务正常运行且可访问
2. 确保 Sa-Token 配置正确
3. 排除的URI不会进行用户信息获取
4. 建议在生产环境中关闭详细日志以提高性能

## 依赖要求

- Spring Cloud Gateway
- Sa-Token
- OpenFeign
- Hutool (用于Base64编码和JSON处理)
