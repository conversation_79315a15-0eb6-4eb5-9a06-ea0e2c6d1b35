spring:
  application:
    name: gateway
  cloud:
    # Nacos 服务发现地址
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    # 开启服务发现路由
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        # 统一认证中心
        - id: auth-server
          uri: http://127.0.0.1:8001
          predicates:
            - Path=/api/auth/**
        # 用户中心服务
        - id: user-server                  # 路由id，唯一标识
          uri: http://127.0.0.1:8002            # 目标服务地址
          predicates: # 路由断言，匹配请求路径
            - Path=/api/user/**                   # 匹配请求路径

server:
  port: 8000

sa-token:
#  # API Key 相关配置
#  api-key:
#    prefix: AK- # API Key 前缀
#    timeout: 3600000 # 有效期（单位：秒） 默认30天，-1 代表永久有效、
#    is-record-index: true # 是否记录请求的 index，可用于查询
#  # 参数签名配置
  sign:
    secret-key: 1234567890123456 # 签名密钥
  token-name: Authorization # token 名称（同时也是 cookie 名称）
  token-prefix: Bearer # token前缀
  timeout: 3600000 # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  active-timeout: -1 # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  is-concurrent: true # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-share: false # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  token-style: uuid # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  is-log: true # 是否输出操作日志

auth:
  excludeUris:
      - /api/auth/**

# 用户服务配置
user:
  service:
    baseUrl: http://127.0.0.1:8002