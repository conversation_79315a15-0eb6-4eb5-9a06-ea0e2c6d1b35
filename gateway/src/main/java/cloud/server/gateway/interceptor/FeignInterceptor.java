package cloud.server.gateway.interceptor;

import cn.dev33.satoken.same.SaSameUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

/**
 * Feign拦截器，在feign发起请求之前，加入一些操作
 */
@Component
public class FeignInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        template.header(SaSameUtil.SAME_TOKEN, SaSameUtil.getToken());
    }
}
