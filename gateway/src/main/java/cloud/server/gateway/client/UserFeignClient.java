package cloud.server.gateway.client;

import cloud.server.gateway.client.vo.UserVo;
import cloud.server.gateway.interceptor.FeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(
        name = "userService",
        url = "${user.service.baseUrl}",
        configuration = FeignInterceptor.class
)
public interface UserFeignClient {
    @GetMapping("/api/user/{userId}")
    UserVo getUserById(@PathVariable("userId") Long userId);
}
