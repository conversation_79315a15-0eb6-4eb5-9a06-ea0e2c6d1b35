package cloud.server.gateway.client;

import lombok.Data;

@Data
public class ApiResponse<T> {
    private Integer code;
    private String msg;
    private T data;

    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setCode(200);
        apiResponse.setMsg("success");
        apiResponse.setData(data);
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail() {
        return fail(null);
    }

    public static <T> ApiResponse<T> fail(String msg) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setCode(500);
        apiResponse.setMsg(msg);
        return apiResponse;
    }
}
