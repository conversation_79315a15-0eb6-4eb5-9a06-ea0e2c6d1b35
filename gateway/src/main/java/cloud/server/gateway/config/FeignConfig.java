package cloud.server.gateway.config;

import cloud.server.gateway.client.ApiResponse;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonSyntaxException;
import feign.Response;
import feign.Retryer;
import feign.Util;
import feign.codec.Decoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.nio.charset.StandardCharsets;

/**
 * Feign 配置类
 */
@Configuration
public class FeignConfig {
    private static final Logger log = LoggerFactory.getLogger(FeignConfig.class);

    /**
     * Feign 重试器
     * @return Feign 重试器
     */
    @Bean
    public Retryer feignRetryer() {
        // 最大请求次数为3，初始间隔时间为100ms，重试间最大间隔时间为1s
        return new Retryer.Default(100, 1000, 3);
    }

    /**
     * Feign 解码器；用于处理统一响应格式
     * @return Feign 解码器
     */
    @Bean
    public Decoder feignDecoder() {
        return (response, type) -> {
            // 读取原始响应体
            String json = feign.Util.toString(response.body().asReader(Util.UTF_8));
            try {
                // 尝试解析为统一响应格式
                ApiResponse<?> result = new Gson().fromJson(json, ApiResponse.class);
                // 如果响应码不是 0 或 200，抛出异常
                if (result.getCode() == 0 || result.getCode() == 200) {
                    return new Gson().fromJson(new Gson().toJson(result.getData()), type);
                }
                throw new RuntimeException(result.getMsg());
            } catch (JsonSyntaxException e) {
                // 如果解析失败，返回原始响应体
                return new Gson().fromJson(json, type);
            }
        };
    }
}
