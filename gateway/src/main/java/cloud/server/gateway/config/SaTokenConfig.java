package cloud.server.gateway.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

/**
 * [Sa-Token 权限认证] 配置类
 * 注意：此配置类已被 ConfigurableSaTokenFilter 替代，暂时禁用
 */
//@Configuration
@ConfigurationProperties(prefix = "auth")
@Data
public class SaTokenConfig {
    private List<String> excludeUris;

    //@Bean
    public SaReactorFilter saReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**") // 拦截全部路径
                // 开放地址
                .setExcludeList(excludeUris) // 排除 favicon.ico
                // 鉴权方法：每次访问进入
                .setAuth(obj -> {
                    StpUtil.checkLogin();
                })
                .setError(e -> {
                    if (e instanceof NotLoginException) {
                        SaHolder.getResponse().setStatus(HttpStatus.UNAUTHORIZED.value());
                        return SaResult.error("未授权").setCode(HttpStatus.UNAUTHORIZED.value());
                    }
                    return SaResult.error(e.getMessage()).setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
                });
    }
}
